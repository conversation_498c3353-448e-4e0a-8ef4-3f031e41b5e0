package com.yxt.order.assistant.server.rocketmq;


import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "TP_ORDER_XFRAME2_group", topic = "TP_ORDER_XFRAME2",  consumeMode = ConsumeMode.CONCURRENTLY)
public class UpdateOrderDomainEventConsumer extends AbstractRocketMQListenerEnhance {

  @Override
  public void handleMsg(String message) {
    System.out.println("UpdateOrderDomainEventConsumer event:" + message);

  }

}
