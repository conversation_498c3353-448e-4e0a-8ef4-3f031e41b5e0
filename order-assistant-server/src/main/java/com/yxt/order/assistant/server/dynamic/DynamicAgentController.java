package com.yxt.order.assistant.server.dynamic;

import com.yxt.dynamic.routing.proxy.DynamicRouteContext;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.assistant.server.knowledge.AbstractController;
import com.yxt.order.assistant.server.rocketmq.TopicTest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping("/test")
public class DynamicAgentController extends AbstractController {

  private static final AtomicInteger interceptorCallCount = new AtomicInteger(0);
  private static final AtomicBoolean contextCleared = new AtomicBoolean(false);
  @Resource
  private RocketMQTemplate rocketMQTemplate;

  @PostMapping("/testSendMQ")
  public void testSendMQ() {
    Object data = "234234";
    String msg = JsonUtils.toJson(data);
    // 发送到这个topic,然后根据type来处理,默认只处理一次
    SendResult sendResult = rocketMQTemplate.syncSend(TopicTest.topic, msg, 6000);
    System.out.println("sendResult: " + sendResult);
  }

  /**
   * 通过反射验证RocketMQ相关类的增强状态
   */
  @PostMapping("/testRocketMQClassEnhancement")
  public void testRocketMQClassEnhancement() {
    // 测试DefaultMQProducer
    try {
      Class<?> producerClass = DefaultMQProducer.class;
      Field[] fields = producerClass.getDeclaredFields();
      boolean hasEnhancementField = false;
      for (Field field : fields) {
        if ("dynasticNotUse".equals(field.getName())) {
          hasEnhancementField = true;
          System.out.println("✓ DefaultMQProducer字节码增强成功");
          break;
        }
      }

      if (!hasEnhancementField) {
        System.out.println("⚠ DefaultMQProducer可能未被字节码增强");
      }

    } catch (Exception e) {
      System.out.println("⚠ 无法检查DefaultMQProducer，可能RocketMQ未在classpath中: " + e.getMessage());
    }

    // 测试ConsumeMessageConcurrentlyService
    try {
      Class<?> consumeServiceClass = Class.forName("org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest");
      Field[] fields = consumeServiceClass.getDeclaredFields();
      boolean hasEnhancementField = false;
      for (Field field : fields) {
        if ("dynasticNotUse".equals(field.getName())) {
          hasEnhancementField = true;
          System.out.println("✓ ConsumeMessageConcurrentlyService$ConsumeRequest字节码增强成功");
          break;
        }
      }

      if (!hasEnhancementField) {
        System.out.println("⚠ ConsumeMessageConcurrentlyService$ConsumeRequest可能未被字节码增强");
      }

    } catch (ClassNotFoundException e) {
      System.out.println("⚠ ConsumeMessageConcurrentlyService$ConsumeRequest类未找到，可能RocketMQ未在classpath中");
    }
  }

  /**
   * 验证路由上下文清理功能
   */
  @PostMapping("/testDynamicRouteContextClear")
  public void testDynamicRouteContextClear() {
    try {
      // 尝试调用DynamicRouteContext.clear()
      Method clearMethod = DynamicRouteContext.class.getMethod("clear");
      clearMethod.invoke(null);
      System.out.println("✓ DynamicRouteContext.clear()调用成功");
    } catch (Exception e) {
      System.out.println("⚠ DynamicRouteContext.clear()调用失败: " + e.getMessage());
    }
  }


//  @Before
//  public void setUp() {
//    interceptorCallCount.set(0);
//    contextCleared.set(false);
//    // 重置路由上下文
//    try {
//      DynamicRouteContext.clear();
//    } catch (Exception e) {
//      // 忽略，可能类还未加载
//    }
//  }
//
//  @After
//  public void tearDown() {
//    interceptorCallCount.set(0);
//    contextCleared.set(false);
//  }

  /**
   * 测试OkHttpClient是否真的被注入了拦截器 // 测试成功
   */
  @PostMapping("/testOkHttpClient")
  public void testOkHttpClientRealInterceptorInjection() throws Exception {
    // Given - 创建一个真实的OkHttpClient
    OkHttpClient.Builder builder = new OkHttpClient.Builder();
    OkHttpClient client = builder.build();

    // When - 检查是否存在dynasticNotUse字段（字节码增强的标识）
    Field[] fields = OkHttpClient.Builder.class.getDeclaredFields();
    boolean hasDynasticField = false;
    for (Field field : fields) {
      if ("dynasticNotUse".equals(field.getName())) {
        hasDynasticField = true;
        break;
      }
    }

    // Then - 如果字节码增强成功，应该有这个标识字段
    if (hasDynasticField) {
      System.out.println("✓ OkHttpClient字节码增强成功，发现dynasticNotUse字段");
    } else {
      System.out.println("⚠ OkHttpClient可能未被字节码增强");
    }

    // 验证拦截器数量（如果增强成功，会自动添加拦截器）
    int interceptorCount = client.interceptors().size();
    System.out.println("OkHttpClient拦截器数量: " + interceptorCount);

    // 请求地址

    // 准备 JSON 数据
    String jsonString = "{\n" +
        "    \"name\": \"张三\",\n" +
        "    \"age\": 25,\n" +
        "    \"email\": \"<EMAIL>\"\n" +
        "}";

    // 创建 RequestBody
    MediaType JSON = MediaType.get("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(jsonString, JSON);

    // 创建 Request
    Request request = new Request.Builder()
        // {"http://127.0.0.1:1111":"http://127.0.0.1:8086"}
        .url("http://127.0.0.1:1111/test/dynamic")  // 测试用的 API 端点 1111是错误的,会在route中设置为正确的8086
        .post(body)
        .addHeader("Content-Type", "application/json")
        .addHeader("User-Agent", "OkHttp-Demo/1.0")
        .build();

    try (Response response = client.newCall(request).execute()) {
      if (response.isSuccessful()) {
        String responseBody = response.body().string();
        System.out.println("请求成功！");
        System.out.println("响应码：" + response.code());
        System.out.println("响应内容：" + responseBody);
      } else {
        System.out.println("请求失败，状态码：" + response.code());
      }
    } catch (IOException e) {
      System.out.println("请求发生异常：" + e.getMessage());
      e.printStackTrace();
    }

  }

  /**
   * 测试ThreadPoolExecutor是否被真实增强 //测试成功
   */
  @PostMapping("/testThreadPoolExecutor")
  public void testThreadPoolExecutorRealEnhancement() throws Exception {
    // Given - 创建真实的线程池
    ThreadPoolExecutor executor = new ThreadPoolExecutor(
        1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>()
    );

    // 检查是否有dynasticNotUse字段
    Field[] fields = ThreadPoolExecutor.class.getDeclaredFields();
    boolean hasEnhancementField = false;
    for (Field field : fields) {
      if ("dynasticNotUse".equals(field.getName())) {
        hasEnhancementField = true;
        field.setAccessible(true); // java9之后再java.base目录下,不能增强成功
        System.out.println("✓ ThreadPoolExecutor字节码增强成功，dynasticNotUse = " + field.get(null));
        break;
      }
    }

    if (!hasEnhancementField) {
      System.out.println("⚠ ThreadPoolExecutor可能未被字节码增强");
    }

    // When - 执行任务
    CountDownLatch latch = new CountDownLatch(1);
    AtomicBoolean taskExecuted = new AtomicBoolean(false);

    executor.execute(() -> {
      taskExecuted.set(true);
      latch.countDown();
    });

    // Then
    System.out.println("任务应该被执行"+ latch.await(5, TimeUnit.SECONDS));
    System.out.println("任务应该成功执行"+ taskExecuted.get());

    executor.shutdown();
  }

  /**
   * 测试Spring ThreadPoolTaskExecutor的真实增强  // 测试成功
   */
  @PostMapping("/testSpringThreadPoolTaskExecutor")
  public void testSpringThreadPoolTaskExecutorRealEnhancement() throws Exception {
    // Given
    ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
    taskExecutor.setCorePoolSize(1);
    taskExecutor.setMaxPoolSize(1);
    taskExecutor.setQueueCapacity(10);
    taskExecutor.setThreadNamePrefix("test-");
    taskExecutor.initialize();

    // 检查字节码增强标识
    Field[] fields = ThreadPoolTaskExecutor.class.getDeclaredFields();
    boolean hasEnhancementField = false;
    for (Field field : fields) {
      if ("dynasticNotUse".equals(field.getName())) {
        hasEnhancementField = true;
        System.out.println("✓ ThreadPoolTaskExecutor字节码增强成功");
        break;
      }
    }

    if (!hasEnhancementField) {
      System.out.println("⚠ ThreadPoolTaskExecutor可能未被字节码增强");
    }

    // When - 执行任务
    CountDownLatch latch = new CountDownLatch(3);

    taskExecutor.execute(() -> latch.countDown());
    taskExecutor.submit(() -> latch.countDown());
    taskExecutor.submitListenable(() -> {
      latch.countDown();
      return null;
    });

    // Then
    System.out.println("所有任务应该被执行"+ latch.await(10, TimeUnit.SECONDS));

    taskExecutor.shutdown();
  }

  /**
   * 测试ForkJoinPool的真实增强 // 测试成功
   */
  @PostMapping("/testForkJoinPoolRealEnhancement")
  public void testForkJoinPoolRealEnhancement() throws Exception {
    // Given
    ForkJoinPool forkJoinPool = new ForkJoinPool(2);

    // 检查字节码增强
    Field[] fields = ForkJoinPool.class.getDeclaredFields();
    boolean hasEnhancementField = false;
    for (Field field : fields) {
      if ("dynasticNotUse".equals(field.getName())) {
        hasEnhancementField = true;
        System.out.println("✓ ForkJoinPool字节码增强成功");
        break;
      }
    }

    if (!hasEnhancementField) {
      System.out.println("⚠ ForkJoinPool可能未被字节码增强");
    }

    // When
    CountDownLatch latch = new CountDownLatch(2);

    forkJoinPool.execute(() -> latch.countDown());
    forkJoinPool.submit(() -> latch.countDown());

    // Then
    System.out.println("ForkJoinPool任务应该被执行"+ latch.await(5, TimeUnit.SECONDS));

    forkJoinPool.shutdown();
  }

  /**
   * 测试AbstractExecutorService的真实增强
   */
  @PostMapping("/testAbstractExecutorServiceRealEnhancement")
  public void testAbstractExecutorServiceRealEnhancement() throws Exception {
    // Given
    ExecutorService executor = Executors.newSingleThreadExecutor();

    // 获取实际的实现类
    Class<?> actualClass = executor.getClass();
    System.out.println("ExecutorService实际类型: " + actualClass.getName());

    // 检查是否有增强标识（可能在父类中）
    boolean hasEnhancementField = false;
    Class<?> checkClass = actualClass;
    while (checkClass != null && !hasEnhancementField) {
      Field[] fields = checkClass.getDeclaredFields();
      for (Field field : fields) {
        if ("dynasticNotUse".equals(field.getName())) {
          hasEnhancementField = true;
          System.out.println("✓ " + checkClass.getSimpleName() + "字节码增强成功");
          break;
        }
      }
      checkClass = checkClass.getSuperclass();
    }

    if (!hasEnhancementField) {
      System.out.println("⚠ AbstractExecutorService可能未被字节码增强");
    }

    // When
    Future<String> future = executor.submit(() -> "测试结果");

    // Then
    String result = future.get(5, TimeUnit.SECONDS);
    System.out.println("测试结果"+ result);

    executor.shutdown();
  }


  /**
   * 综合测试：模拟真实场景的执行流程 // 测试成功
   */
  @PostMapping("/testRealScenarioExecution")
  public void testRealScenarioExecution() throws Exception {
    System.out.println("\n=== 开始综合场景测试 ===");

    // 1. 测试HTTP客户端
    try {
      OkHttpClient client = new OkHttpClient.Builder()
          .connectTimeout(1, TimeUnit.SECONDS)
          .build();

      System.out.println("HTTP客户端创建成功，拦截器数量: " + client.interceptors().size());
    } catch (Exception e) {
      System.out.println("HTTP客户端测试异常: " + e.getMessage());
    }

    // 2. 测试线程池执行
    CountDownLatch allTasksLatch = new CountDownLatch(4);

    // ThreadPoolExecutor
    ThreadPoolExecutor tpe = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    tpe.execute(() -> {
      System.out.println("ThreadPoolExecutor任务执行");
      allTasksLatch.countDown();
    });

    // Spring TaskExecutor
    ThreadPoolTaskExecutor springExecutor = new ThreadPoolTaskExecutor();
    springExecutor.setCorePoolSize(1);
    springExecutor.setMaxPoolSize(1);
    springExecutor.initialize();
    springExecutor.execute(() -> {
      System.out.println("Spring ThreadPoolTaskExecutor任务执行");
      allTasksLatch.countDown();
    });

    // ExecutorService
    ExecutorService es = Executors.newSingleThreadExecutor();
    es.submit(() -> {
      System.out.println("ExecutorService任务执行");
      allTasksLatch.countDown();
    });

    // ForkJoinPool
    ForkJoinPool fjp = new ForkJoinPool();
    fjp.execute(() -> {
      System.out.println("ForkJoinPool任务执行");
      allTasksLatch.countDown();
    });

    // 等待所有任务完成
    System.out.println("所有线程池任务应该在10秒内完成"+ allTasksLatch.await(10, TimeUnit.SECONDS));

    // 清理资源
    tpe.shutdown();
    springExecutor.shutdown();
    es.shutdown();
    fjp.shutdown();

    System.out.println("=== 综合场景测试完成 ===\n");
  }

  /**
   * 性能对比测试：验证字节码增强的性能影响
   */
  @PostMapping("/testPerformanceImpact")
  public void testPerformanceImpact() throws Exception {
    System.out.println("\n=== 性能影响测试 ===");

    int iterations = 1000;

    // 测试普通线程池性能
    ThreadPoolExecutor normalPool = new ThreadPoolExecutor(4, 4, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    long startTime = System.currentTimeMillis();
    CountDownLatch latch = new CountDownLatch(iterations);

    for (int i = 0; i < iterations; i++) {
      normalPool.execute(() -> {
        // 模拟简单工作负载
        Math.random();
        latch.countDown();
      });
    }

    System.out.println("性能测试应该在30秒内完成"+ latch.await(30, TimeUnit.SECONDS));

    long endTime = System.currentTimeMillis();
    long duration = endTime - startTime;

    System.out.println("执行" + iterations + "个任务耗时: " + duration + "ms");
    System.out.println("平均每个任务耗时: " + (duration * 1.0 / iterations) + "ms");

    System.out.println("性能应该在可接受范围内"+ (duration < 30000));

    normalPool.shutdown();

    System.out.println("=== 性能影响测试完成 ===\n");
  }

  /**
   * 验证字节码增强的全局状态
   */
  @PostMapping("/testMdcFutureTask")
  public void testMdcFutureTask()
      throws ExecutionException, InterruptedException, TimeoutException {

    // 定义一个任务
    Callable<Integer> callableTask = new Callable<Integer>() {
      @Override
      public Integer call() throws Exception {
        System.out.println("任务正在执行...");
        Thread.sleep(2000);  // 模拟任务执行过程
        return 42;  // 返回结果
      }
    };

    // 创建 FutureTask 对象
    FutureTask<Integer> futureTask = new FutureTask<>(callableTask);

    // 启动新线程来执行任务
    Thread thread = new Thread(futureTask);
    thread.start();

    // 在这里可以做其他事情...

    // 获取任务的执行结果
    Integer result = futureTask.get();  // 如果任务还没完成，会阻塞直到结果可用
    System.out.println("任务的结果是: " + result);

  }

  /**
   * 验证字节码增强的全局状态
   */
  @PostMapping("/testGlobalEnhancementStatus")
  public void testGlobalEnhancementStatus() {
    System.out.println("\n=== 字节码增强状态检查 ===");

    String[] targetClasses = {
        "okhttp3.OkHttpClient$Builder",
        "java.util.concurrent.ThreadPoolExecutor",
        "java.util.concurrent.AbstractExecutorService",
        "org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor",
        "java.util.concurrent.ForkJoinPool"
    };

    int enhancedCount = 0;

    for (String className : targetClasses) {
      try {
        Class<?> clazz = Class.forName(className);
        Field[] fields = clazz.getDeclaredFields();
        boolean hasEnhancementField = false;

        for (Field field : fields) {
          if ("dynasticNotUse".equals(field.getName())) {
            hasEnhancementField = true;
            enhancedCount++;
            break;
          }
        }

        System.out.println(className + ": " + (hasEnhancementField ? "✓ 已增强" : "⚠ 未增强"));

      } catch (ClassNotFoundException e) {
        System.out.println(className + ": ⚠ 类未找到");
      }
    }

    System.out.println("总体增强状态: " + enhancedCount + "/" + targetClasses.length + " 个类被成功增强");
    System.out.println("=== 状态检查完成 ===\n");

    // 如果没有任何类被增强，说明字节码增强器可能没有正确工作
    if (enhancedCount == 0) {
      System.out.println("⚠ 警告：没有发现任何被增强的类，请检查字节码增强器是否正确配置和运行");
    }
  }

}
