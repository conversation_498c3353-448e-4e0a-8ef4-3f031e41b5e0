package com.yxt.order.assistant.server.knowledge.req;

import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建知识库
 */
@Data
@ApiModel("动态请求参数")
public class DynamicReq {

  private String name;

}
