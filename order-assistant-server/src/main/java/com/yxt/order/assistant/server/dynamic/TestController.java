package com.yxt.order.assistant.server.dynamic;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.AbstractController;
import com.yxt.order.assistant.server.knowledge.req.DynamicReq;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class TestController extends AbstractController {
  /**
   * 创建知识库
   */
  @PostMapping("/dynamic")
  public ResponseBase<Boolean> dynamic(@RequestBody DynamicReq req, @RequestHeader HttpHeaders headers) {

    return generateSuccess(Boolean.TRUE);
  }

}
